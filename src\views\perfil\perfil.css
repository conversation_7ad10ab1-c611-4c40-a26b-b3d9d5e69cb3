/* Perfil - Estilos */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: sans-serif;
  background: #fff;
  padding-bottom: 120px;
}

header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  font-weight: bold;
  font-size: 1.3rem;
  border-bottom: 1px solid #ddd;
  position: relative;
}

.back-button {
  position: absolute;
  left: 16px;
  font-size: 20px;
  background: none;
  border: none;
  cursor: pointer;
}

.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 1rem;
}

.profile {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;
}

.profile-img {
  width: 96px;
  height: 96px;
  object-fit: cover;
  border-radius: 8px;
}

.profile-info {
  flex: 1;
}

.profile-info label {
  display: block;
  font-size: 0.9rem;
  margin-bottom: 4px;
  font-weight: bold;
}

.profile-info input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 10px;
  font-size: 1rem;
}

.section-title {
  margin-top: 24px;
  margin-bottom: 12px;
  font-size: 1.1rem;
  font-weight: bold;
  text-align: center;
  border-top: 1px solid #ddd;
  padding-top: 12px;
}

.inspection-card {
  display: flex;
  gap: 1rem;
  align-items: center;
  background: #f5f5f5;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  cursor: pointer;
}

.inspection-img {
  width: 100px;
  height: 70px;
  object-fit: cover;
  border-radius: 8px;
}

.inspection-content h3 {
  margin: 0;
  font-size: 1rem;
}

.inspection-content p {
  margin: 4px 0 0;
  font-size: 0.9rem;
}

.status {
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 8px;
  margin-left: 8px;
}

.status-nao {
  background: #666;
  color: white;
}

.status-andamento {
  background: #f4a300;
  color: white;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px;
  font-weight: bold;
  border-bottom: 1px solid #ccc;
}

.back-button {
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 16px;
}

.container {
  padding: 16px;
  max-width: 480px;
  margin: 0 auto;
}

.profile {
  display: flex;
  gap: 16px;
  align-items: flex-start;
  margin-bottom: 24px;
}

.profile-img {
  width: 96px;
  height: 96px;
  border-radius: 8px;
  object-fit: cover;
}

.profile-info label {
  font-weight: bold;
  display: block;
  margin-bottom: 4px;
  margin-top: 8px;
}

.profile-info input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
}

.section-title {
  font-size: 18px;
  margin: 16px 0 8px;
  border-top: 1px solid #ccc;
  padding-top: 16px;
}

.inspection-card {
  display: flex;
  gap: 12px;
  background: #f0f0f0;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 16px;
  align-items: center;
}

.inspection-img {
  width: 100px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
}

.inspection-content h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
}

.inspection-content p {
  margin: 0;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.status-nao {
  background-color: #555;
}

.status-andamento {
  background-color: #fbbf24;
  color: black;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}

/* Global Styles - CTRL + S */

/* Reset e configurações básicas */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #fff;
  color: #333;
  line-height: 1.6;
}

/* Cores do projeto */
:root {
  --primary-color: #44799c;
  --secondary-color: #f72585;
  --success-color: #28a745;
  --warning-color: #f4a300;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --gray-color: #666;
  --border-color: #ddd;
  --background-light: #f5f7fa;
}

/* Headers padrão */
header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  font-weight: bold;
  font-size: 1.3rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: #fff;
}

.back-button, .back-btn {
  position: absolute;
  left: 16px;
  font-size: 20px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--dark-color);
}

/* Container principal */
.container {
  max-width: 480px;
  margin: 0 auto;
  padding: 1rem;
}

/* Botões padrão */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: #3a6b8c;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: #e01e6b;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

/* Inputs padrão */
input, textarea, select {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(68, 121, 156, 0.2);
}

/* Cards padrão */
.card {
  background: #fff;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Status tags */
.status {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
}

.status-nao-iniciado {
  background-color: var(--gray-color);
}

.status-em-andamento {
  background-color: var(--warning-color);
}

.status-concluido {
  background-color: var(--success-color);
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: var(--light-color);
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid var(--border-color);
  z-index: 1000;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
  color: var(--dark-color);
  transition: color 0.3s ease;
}

.bottom-nav div:hover {
  color: var(--primary-color);
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}

/* Utilitários */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* Responsividade */
@media (max-width: 480px) {
  .container {
    padding: 0.5rem;
  }
  
  header {
    font-size: 1.2rem;
    padding: 12px;
  }
  
  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

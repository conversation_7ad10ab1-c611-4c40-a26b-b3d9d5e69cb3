<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Inspeções</title>
  <link rel="stylesheet" href="inspecoes.css">
</head>
<body>

  <header>Inspeções</header>

  <main>
    <div class="filtros">
      <button class="ativo" onclick="filtrar('todos')">Todos</button>
      <button onclick="filtrar('a-fazer')">A Fazer</button>
      <button onclick="filtrar('em-andamento')">Em Andamento</button>
      <button onclick="filtrar('concluido')">Concluído</button>
    </div>

    <div class="pesquisa">
      <input type="text" placeholder="Pesquisar">
    </div>

    <!-- Card A Fazer -->
    <div class="inspecao a-fazer" onclick="window.location.href='inspecao.html'">
      <img id="img-inspecao" src="https://via.placeholder.com/400x200" alt="Imagem">
      <div class="conteudo">
        <h3>Inspeção BPO</h3>
        <div class="info">
          <span>22/08/2024</span>
          <span class="tag nao-iniciado">Não Iniciado</span>
        </div>
      </div>
    </div>

    <!-- Card Em Andamento -->
    <div class="inspecao em-andamento" onclick="alert('Futuramente abrirá a tela da inspeção em andamento')">
      <img src="https://via.placeholder.com/400x200/f4a300/ffffff?text=Em+Andamento" alt="Imagem inspeção em andamento">
      <div class="conteudo" style="background:#f4a300; color:white;">
        <h3>Inspeção Elétrica</h3>
        <div class="info">
          <span>21/08/2024</span>
          <span class="tag">Em Andamento</span>
        </div>
      </div>
    </div>

    <!-- Card Concluído -->
    <div class="inspecao concluido" onclick="alert('Futuramente abrirá a tela da inspeção concluída')">
      <img src="https://via.placeholder.com/400x200/003366/ffffff?text=Concluído" alt="Imagem inspeção concluída">
      <div class="conteudo" style="background:#003366; color:white;">
        <h3>Inspeção Concluída</h3>
        <div class="info">
          <span>20/08/2024</span>
          <span class="tag">Concluído</span>
        </div>
      </div>
    </div>
  </main>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="/assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../perfil/perfil.html'">
      <img src="/assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function filtrar(tipo) {
      document.querySelectorAll('.filtros button').forEach(btn => btn.classList.remove('ativo'));
      document.querySelector(`.filtros button[onclick*="${tipo}"]`).classList.add('ativo');

      document.querySelectorAll('.inspecao').forEach(card => {
        if (tipo === 'todos') {
          card.classList.remove('oculto');
        } else {
          card.classList.toggle('oculto', !card.classList.contains(tipo));
        }
      });
    }

    // Carrega imagem salva no localStorage
    document.addEventListener("DOMContentLoaded", () => {
      const imagem = localStorage.getItem("imagemInspecao");
      if (imagem) {
        const imgTag = document.getElementById("img-inspecao");
        imgTag.src = imagem;
      }
    });
  </script>

</body>
</html>

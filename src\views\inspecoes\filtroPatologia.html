<!DOCTYPE html>
<html lang="pt-br">
<head>
    <link rel="stylesheet" href="filtroPatologia.css">
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Selecionar Patologia</title>
  <style>
    * { box-sizing: border-box; }
    body { font-family: sans-serif; margin: 0; padding: 0; background: #fff; }
    header { display: flex; align-items: center; gap: 16px; padding: 16px; font-weight: bold; }
    .back { font-size: 24px; cursor: pointer; }
    .container { padding: 16px; max-width: 480px; margin: 0 auto; }
    h2 { margin-top: 0; font-size: 20px; }

    .accordion {
      border: 1px solid #ddd;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;
    }
    .accordion-header {
      background: #e0e0e0;
      padding: 12px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .accordion-body {
      display: none;
      padding: 12px;
      background: #f9f9f9;
    }
    .accordion-body div {
      padding: 8px 0;
      cursor: pointer;
      color: #555;
    }
    .accordion-body div:hover {
      background-color: #eee;
    }
    .selected {
      background-color: #d0e9ff;
      font-weight: bold;
    }
    .create-link {
      font-size: 14px;
      margin-top: 8px;
    }
    .create-link a {
      color: #0099ff;
      text-decoration: none;
    }
    .create-link a:hover {
      text-decoration: underline;
    }
    .bottom-nav {
      position: fixed;
      bottom: 0;
      width: 100%;
      background-color: #f5f5f5;
      display: flex;
      justify-content: space-around;
      padding: 12px 0;
      border-top: 1px solid #ddd;
    }
    .bottom-nav div { text-align: center; font-size: 14px; cursor: pointer; }
    .bottom-nav img { width: 24px; height: auto; display: block; margin: 0 auto 4px; }
  </style>
</head>
<body>
  <header>
    <span class="back" onclick="window.history.back()">&larr;</span>
    <span>Patologias</span>
  </header>

  <div class="container">
    <h2>Selecione o tipo de patologia</h2>

    <div class="accordion">
      <div class="accordion-header" onclick="toggleAccordion(this)">
        Parede (Sistemas)
        <span>&#x25BC;</span>
      </div>
      <div class="accordion-body">
        <div onclick="selectItem(this)">Cracks</div>
        <div onclick="selectItem(this)">Infiltração</div>
        <div onclick="selectItem(this)">Descolamento</div>
        <div onclick="selectItem(this)">Manchas</div>
        <div onclick="selectItem(this)">Eflorescência</div>
        <div onclick="selectItem(this)">Bolhas</div>
        <div onclick="selectItem(this)">Desgaste</div>
        <div class="create-link">
          Não encontrou a patologia? <a href="../../src/views/registro.html">Clique aqui para criar uma NOVA</a>
        </div>
      </div>
    </div>

    <div class="accordion">
      <div class="accordion-header" onclick="toggleAccordion(this)">
        Piso (Sistemas)
        <span>&#x25BC;</span>
      </div>
      <div class="accordion-body">
        <div onclick="selectItem(this)">Trincas</div>
        <div onclick="selectItem(this)">Desgaste</div>
      </div>
    </div>

    <div class="accordion">
      <div class="accordion-header" onclick="toggleAccordion(this)">
        Teto (Sistemas)
        <span>&#x25BC;</span>
      </div>
      <div class="accordion-body">
        <div onclick="selectItem(this)">Infiltração</div>
        <div onclick="selectItem(this)">Bolhas</div>
      </div>
    </div>
  </div>

  <div class="bottom-nav">
    <div onclick="alert('Navegar para Inspeções')">
      <img src="/assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="alert('Navegar para Perfil')">
      <img src="/assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function toggleAccordion(header) {
      const body = header.nextElementSibling;
      const icon = header.querySelector('span');
      const isVisible = body.style.display === 'block';
      body.style.display = isVisible ? 'none' : 'block';
      icon.innerHTML = isVisible ? '&#x25BC;' : '&#x25B2;';
    }

    function selectItem(element) {
      const siblings = element.parentNode.querySelectorAll('div');
      siblings.forEach(el => el.classList.remove('selected'));
      element.classList.add('selected');
    
    }
    function selectItem(element) {
  const siblings = element.parentNode.querySelectorAll('div');
  siblings.forEach(el => el.classList.remove('selected'));
  element.classList.add('selected');

  const value = element.innerText;
  localStorage.setItem('patologiaSelecionada', value);
  window.location.href = 'registro.html'; 
}
  </script>
</body>
</html>

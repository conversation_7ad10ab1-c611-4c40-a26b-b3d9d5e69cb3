<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Perfil</title>
  <link rel="stylesheet" href="perfil.css">
</head>
<body>

  <header>
    <button class="back-button" onclick="history.back()">←</button>
    Perfil
  </header>

  <main class="container">
    <section class="profile">
      <label for="uploadFoto">
        <img src="../assets/img/perfil.jpg" alt="Foto do usuário" class="profile-img" id="fotoPerfil">
      </label>
      <input type="file" id="uploadFoto" accept="image/*" style="display: none" onchange="previewFoto(event)">
      <div class="profile-info">
        <label for="nome">Nome Completo</label>
        <input type="text" id="nome" placeholder="Digite seu nome">

        <label for="email">Email</label>
        <input type="email" id="email" placeholder="<EMAIL>">
      </div>
    </section>

    <h2 class="section-title">Inspeções em andamento</h2>

    <section class="inspection-card" onclick="location.href='registro.html'">
      <img src="/assets/img/predio1.jpg" alt="Imagem inspeção" class="inspection-img">

      <div class="inspection-content">
        <h3>Inspeção BPO</h3>
        <p>22/08/2024 <span class="status status-nao">Não Iniciado</span></p>
      </div>
    </section>

    <section class="inspection-card" onclick="location.href='registro.html'">
      <img src="/assets/img/predio1.jpg" alt="Imagem inspeção" class="inspection-img">
      <div class="inspection-content">
        <h3>Inspeção xpto</h3>
        <p>22/08/2024 <span class="status status-andamento">Em Andamento</span></p>
      </div>
    </section>
  </main>

  <footer class="bottom-nav">
    <div onclick="location.href='../inspecoes/inspecoes.html'">
      <img src="/assets/Icons/pasteS.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='perfil.html'">
      <img src="/assets/Icons/profileS.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </footer>

  <script>
    function previewFoto(event) {
      const foto = document.getElementById('fotoPerfil');
      foto.src = URL.createObjectURL(event.target.files[0]);
    }

    document.addEventListener("DOMContentLoaded", () => {
      const nomeInput = document.getElementById('nome');
      const emailInput = document.getElementById('email');

      const nomeSalvo = localStorage.getItem('perfilNome');
      const emailSalvo = localStorage.getItem('perfilEmail');
      if (nomeSalvo) nomeInput.value = nomeSalvo;
      if (emailSalvo) emailInput.value = emailSalvo;

      nomeInput.addEventListener('input', () => {
        localStorage.setItem('perfilNome', nomeInput.value);
      });

      emailInput.addEventListener('input', () => {
        localStorage.setItem('perfilEmail', emailInput.value);
      });
    });
  </script>
</body>
</html>

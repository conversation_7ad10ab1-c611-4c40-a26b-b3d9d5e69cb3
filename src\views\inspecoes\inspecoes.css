/* Inspeções - Estilos */
* { 
  box-sizing: border-box; 
}

body {
  font-family: sans-serif;
  margin: 0;
  padding: 0;
  background: #fff;
  padding-bottom: 80px;
}

header {
  text-align: center;
  padding: 16px;
  font-size: 1.5rem;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

main {
  max-width: 480px;
  margin: 0 auto;
}

.filtros {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding: 1rem;
}

.filtros button {
  border: none;
  background: #f0f0f0;
  padding: 0.5rem 1rem;
  border-radius: 999px;
  font-size: 0.9rem;
  cursor: pointer;
}

.filtros button.ativo {
  background-color: #44799c;
  color: white;
}

.pesquisa {
  padding: 0 1rem;
}

.pesquisa input {
  width: 100%;
  padding: 0.8rem;
  border-radius: 12px;
  border: none;
  background: #f1f1f1;
  font-size: 1rem;
}

.inspecao {
  margin: 1rem;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #ddd;
}

.inspecao img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.conteudo {
  padding: 0.8rem;
  background-color: #f5f7fa;
}

.conteudo h3 {
  margin: 0;
  font-size: 1.1rem;
}

.info {
  display: flex;
  justify-content: space-between;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  align-items: center;
}

.tag {
  padding: 0.3rem 0.6rem;
  border-radius: 8px;
  font-size: 0.75rem;
  color: white;
}

.nao-iniciado { 
  background-color: #666; 
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  border-top: 1px solid #ddd;
}

.bottom-nav div {
  text-align: center;
  font-size: 14px;
  cursor: pointer;
}

.bottom-nav img {
  width: 24px;
  height: auto;
  display: block;
  margin: 0 auto 4px;
}

.oculto { 
  display: none; 
}

/* Estilos específicos para cards com status */
.conteudo.em-andamento {
  background: #f4a300;
  color: white;
}

.conteudo.concluido {
  background: #003366;
  color: white;
}

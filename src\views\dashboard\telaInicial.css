body {
  font-family: 'Inter', Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
}

.menu {
  display: flex;
  justify-content: space-between; /* logo à esquerda, links à direita */
  align-items: center;
  padding: 10px 40px;
  height: 100px;
}

.nav-logo img {
  height: 140px; /* ajuste o tamanho da logo como preferir */
  margin-left: 10px;
  margin-top: 20px;
}

.opcoes{
  display: flex;
  gap: 70px;
  margin: 0 100px;
  margin-top: 100px 0px ;
}

.opcoes a {
  color: #000;
  text-decoration: none;
  font-size: 17px;
  font-family: sans-serif;
  transition: 0.3s;
}

.opcoes a:hover {
  text-decoration: underline;
  font-size: 160%;
  color: #7f7f7f;
}

.menu {
  display: flex;
  justify-content: space-between; /* logo à esquerda, links à direita */
  align-items: center;
  padding: 10px 40px;
  height: 100px;
}

.nav-logo img {
  height: 140px; /* ajuste o tamanho da logo como preferir */
  margin-left: 10px;
  margin-top: 20px;
}

.destaque {
  position: relative;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.img-azul {
  width: 100%;
  border-radius: 40px 40px 0 0;
  display: block;
}

.img-cidade {
  width: 79.6%; /* ajuste conforme o necessário */
  display: block;
  margin-left: auto; /* isso joga a imagem para a direita */
  border-radius: 0 0 40px 40px;
  margin-top: -32%; /* sobe a imagem pra colar na azul */
}

/* Texto sobreposto */
.titulo {
  position: absolute;
  top: 25%;
  left: 4.1%;
  color: white;
  font-size: 2vw;
  font-weight: 600;
  line-height: 1.2;
  max-width: -1%;
}

.titulo h1 {
  margin: 0;
}

.inspecoes {
  position: absolute;
  top: 84%;
  left: 3.8%;
  color: rgb(0, 0, 0);
  font-size: 2.2vw;
  line-height: 1.2;
}

.inspecoes h1 {
  font-weight: bold;
  margin: 0;
}

.acessar {
  position: absolute;
  top: 88%;
  left: 78%;
  background-color: #E8E8E8;
  padding: 10px 20px;
  border-radius: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: black;
  font-weight: bold;
  font-size: 16px;
  text-decoration: none;
  transition: transform 0.2s ease, background-color 0.2s ease;
}

.acessar:hover {
  transform: scale(1.07); /* cresce de forma proporcional */
  background-color: #d5d5d5;
}

.inspecaoImg {
  width: 100%;
  top: 99%;
  left: 0;
  padding: 20px;
  position: absolute;
  z-index: -1;
  border-radius: 40px;
  display: block;
  display: flex;
  justify-content: space-evenly;
  border-radius: 40px;
}

.inspecaoImg:hover {
  transform: scale(1.03); /* cresce de forma proporcional */
  transition: transform 0.2s ease;
}
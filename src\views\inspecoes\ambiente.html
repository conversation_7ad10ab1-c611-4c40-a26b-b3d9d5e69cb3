<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Ambiente</title>
  <link rel="stylesheet" href="ambiente.css">
</head>
<body>

  <header>
    <span class="back-btn" onclick="history.back()">×</span>
    Ambiente
  </header>

  <main>
    <div class="planta-container">
      <img id="planta-img" src="/assets/planta.png" alt="Planta baixa">
      <p>Desenhe na imagem acima o local onde a patologia foi registrada</p>
    </div>

    <h3>Caso não haja planta, descreva aqui sua posição</h3>
    <textarea id="descricao-local" placeholder="Adicione uma descrição para a sua localização"></textarea>

    <button class="btn" onclick="window.location.href='registro.html'">ADICIONAR LOCALIZAÇÃO</button>
  </main>

  <div class="bottom-nav">
    <div onclick="location.href='inspecoes.html'">
      <img src="/assets/Icons/paste.png" alt="Inspeções">
      <div>Inspeções</div>
    </div>
    <div onclick="location.href='../perfil/perfil.html'">
      <img src="/assets/Icons/profile.png" alt="Perfil">
      <div>Perfil</div>
    </div>
  </div>

  <script>
    function adicionarLocal() {
      const texto = document.getElementById('descricao-local').value.trim();
      const localSalvo = texto !== "" ? texto : "Selecionado na planta";
      localStorage.setItem('localPatologia', localSalvo);
      window.location.href = 'registro.html';
    }
  </script>

</body>
</html>
